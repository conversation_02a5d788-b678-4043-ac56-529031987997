<template>
  <view :class="styles.myContainer">
    <view class="list">
      <navbar title="议题列表" />
      <nut-cell v-for="item in data.list" :key="item" size="large" class="ticketItem" @tap="handleClickTPBtn(item)">
        <view class="ticketItemContent">
          <view class="leftContent" hover-class="none" hover-stop-propagation="false">
            <image mode="widthFix" src="@/assets/images/lpt/ticket.png" />
          </view>
          <view class="rightContent">
            <view class="line1">{{item.topicName }}</view>
            <view class="line3">
              <text>开始时间：</text>
              {{ item.topicStartTime }}</view>
            <view class="line4">
              <text>结束时间：</text>

              {{ item.topicEndTime }}</view>

            <view class="showDetail" @tap="(e) => showDetail(item.files, e)">查看文件内容</view>
          </view>
        </view>
        <view v-if="!item.voteStatus && !dayjs().isBefore(item.topicEndTime)" class="ticketItemBtns3"> 本轮议题投票已过期 </view>
      </nut-cell>
      <nut-empty v-if="data.list.length === 0"></nut-empty>
    </view>
    <nut-popup v-model:visible="data.showPop" position="bottom" closeable round :style="{ height: '300px' }">
      <view class="popContent">
        <rich-text :nodes="data.html" />
      </view>
    </nut-popup>
    <!-- 选择议题  -->
    <searchChoosePop
      v-model:popChooseVisible="data.sheetShow"
      title="选择文件"
      searchPlaceholder="搜索文件名称"
      :choose-list="[data.ytVal]"
      :range="data.ytList"
      range-name="originalName"
      range-value="url"
      @choose-confirm="handleConfimPick"
    />
  </view>

  <!-- toast提示 -->
  <my-toast-components ref="myToast" :duration="2500" />
</template>
<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles
    from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import {
    useAccountStore
} from '@/stores/account';
import {
    getInfo,
    getTopics
} from '@/apis/mine';
import Taro, {
    useDidShow
} from '@tarojs/taro';
import dayjs
    from 'dayjs';
import searchChoosePop
    from '@/components/searchChoosePop/index.vue';

definePageConfig({ backgroundColor: '#f5f5f9' });

const myToast = ref<any>();
const account = useAccountStore();

const data = reactive({
  ytList: [],
  ytVal: {} as any,
  sheetShow: false,
  list: [] as any,
  html: '',
  showPop: false,
  optionsMap: {},
  btnsColorMap: {
    赞成: {
      borderColor: 'rgb(203,231,184)',
      backgroundColor: 'rgb(242,249,236)',
      color: 'rgb(126,192,80)',
    },
    反对: {
      borderColor: 'rgb(245,211,211)',
      backgroundColor: 'rgb(252,240,240)',
      color: 'rgb(228,116,112)',
    },
    弃权: {
      borderColor: 'rgb(211,211,211)',
      backgroundColor: 'rgb(244,244,244)',
      color: 'rgb(145,147,152)',
    },
    随多数: {
      borderColor: 'rgb(186,215,251)',
      backgroundColor: 'rgb(238,245,254)',
      color: 'rgb(90,156,248)',
    },
    废票: {
      borderColor: 'rgb(241,219,182)',
      backgroundColor: 'rgb(252,246,237)',
      color: 'rgb(220,165,80)',
    },
  },
});

const handleClickTPBtn = (item) => {
  Taro.setStorageSync('topicItem', item);
  Taro.navigateTo({ url: '/pages/ticketDetail/index' });
};

const handleConfimPick = (chooseList) => {
  data.ytVal = chooseList[0];
  data.sheetShow = false;
  Taro.downloadFile({
    url: data.ytVal.url,
    success: function (res) {
      const filePath = res.tempFilePath;
      Taro.openDocument({
        filePath: filePath,
        success: function (res) {
          console.log('打开文档成功');
        },
      });
    },
  });
};

const getUserInfo = async () => {
  const res = await getInfo();
  account.userInfo = res;
};

const getYTList = async () => {
  const res = await getTopics();
  data.list = res;
  data.list[0]?.topicOptionsArray.forEach((element) => {
    const label = element.split('-')[0];
    const value = element.split('-')[1];
    data.optionsMap[value] = label;
  });
  // data.ytList = res as any;
  // data.sheetShow = true;
};


const showDetail = (files, e) => {
  e.stopPropagation();
  data.ytList = files;
  data.sheetShow = true;
};

useDidShow(()=>{
  getYTList();
getUserInfo();
});
</script>
