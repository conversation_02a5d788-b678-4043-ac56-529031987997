.container {
  position: fixed;
  bottom: 90px;
  right: -24px;
  z-index: 999;
  transition: right .3s ease;

  &.show {
    right: 5px;
  }

  :global {
    .icon {
      width: 70px;
      height: 70px;
      display: block;

      +.icon {
        margin-top: 4px;
      }

    }

    .fcc {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .full {
      font-size: 11px;
      color: rgba(39, 39, 39, 0.7);
      box-shadow: 0px 1px 7px 0px rgb(225, 226, 229);
      border-radius: 50%;
      background-color: rgba(255, 255, 255, .9);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      height: 86%;
      width: 86%;
    }

    .phoneIcon {
      width: 34px;
      height: 34px;
    }

    .contact {
      font-size: 9px;
      color: rgba(116, 104, 242, .7);
      box-shadow: 0px 1px 7px 0px rgb(225, 226, 229);
      border-radius: 50%;
      background-color: rgba(255, 255, 255, .9);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      height: 86%;
      width: 86%;
    }

  }
}



.post {
  display: flex;
  flex-direction: column;
  color: #222222;
  height: 100%;

  :global {
    .share-content {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 24px 36px 18px;
      margin-bottom: env(safe-area-inset-bottom);

      .share-content-item {
        font-size: 16px;
        font-weight: Regular;
        color: #666666;

        .icon {
          display: block;
          width: 38px;
          height: 38px;
          margin-bottom: 8px;
        }
      }

      .video-text {
        text-align: left;

        text {
          margin-left: 1px;
        }
      }
    }
  }
}



// for share-button.vue
.share {
  display: flex;
  flex-direction: column;
  color: #222222;
  height: 100%;

  :global {
    .footer {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 56px;
      border-top: 1px solid #EEEEEE;
      font-size: 16px;
      color: #999999;
      line-height: 16px;
      box-sizing: content-box;
      padding-bottom: env(safe-area-inset-bottom);
    }

    .border-bottom {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%);
        width: calc(100% - 32px);
        height: 50px;
        border-top: 1px solid #eee;
      }
    }

    .pop-title {
      padding: 14px;
      font-weight: Medium;
    }

    .pop-item-list {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;

      .pop-content-item {
        padding: 14px;
        font-weight: Regular;

        font-size: 14px;
      }
    }

    .share-content {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 24px 36px 18px;

      .share-content-item {
        font-size: 16px;
        color: #666666;

        .icon {
          display: block;
          width: 44px;
          height: 44px;
          margin-bottom: 8px;
        }
      }
    }

    .pop-content {
      display: flex;
      flex-direction: column;
      color: #222222;
      height: 100%;



    }
  }
}


.action {
  display: flex;
  flex-direction: column;
  padding: 0 16px;

  :global {
    .action-item {
      padding: 20px 0;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: center;
    }

    .action-item+.action-item {
      border-top: 1px solid #EEEEEE;
    }

  }
}


.shareFriendsTools {
  width: 375px;
  position: fixed;
  top: 80px;
  left: 0;
  z-index: 4000;
}
