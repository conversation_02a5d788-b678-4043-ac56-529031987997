type BUILD_ENV = typeof process.env.BUILD_ENV;
interface IEnvConfig {
  baseApi: string; // https domain
};

export const allConfigs: { [K in BUILD_ENV as string] : IEnvConfig } = {
  test: {
    baseApi: 'http://127.0.0.1:8080',
  },
  prod: {
    baseApi: 'https://housing-admin.ninthone.cn/prod-api',
  },
};

export const config = allConfigs[process.env.BUILD_ENV as string] ||
  (process.env.NODE_ENV === 'development' ? allConfigs.test : allConfigs.prod);

export const baseApi = config.baseApi;
