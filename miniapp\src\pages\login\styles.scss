.myContainer {
  background-color: #f0f0fa;
  position: relative;

  :global {
    width: 100%;
    height: 100vh;
    background: transparent;

    .head {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 20px 0;

      &>image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 1px dashed #333;
      }

      .headText {
        margin: 15px 0 5px 0;
        color: #333;
        opacity: .3;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .mark1 {
      width: 100%;
      top: 50px;
    }

    .tip {
      margin:  20px 10px;

      .t1 {
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .t2 {
        margin-top: 10px;
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
      }

    }

    .binding {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .txt {
        margin-top: 80px;
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
      }

      .fresh {
        width: 30%;
        margin-top: 30px;
      }
    }


    .info {
      font-size: 16px;
      padding: 0 25px;
      .smstConent {
        display: flex;

        .smsbtn {
          padding: 10px;
        }
      }

      .buttonWidthNum,
      .buttonWidthNum::after {
        background: transparent;
        outline: none;
        border: none;
        border-radius: 0;
        overflow: inherit;
        padding: 0;
        margin: 0;
        color: inherit;
        display: block;
        line-height: 1;
      }

      .input-placeholder{
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .content {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-weight: 600;
        margin-right: 4px;
        margin-left: 4px;
        font-size: 14px;
        color: #333;
        font-weight: 500;
        .nut-input {
          padding: 0;
          background-color: transparent;

          .input-text {
            font-size: 16px;

            width: 100%;
          }
        }
      }

      .t3 {
        margin-top: 34px;
        text-align: center;
        font-size: 13px;
        font-weight: 400;
        color: #666666;
      }

      .btns {
        padding: 40px 0;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .sqBtn {
          // width: 60%;
          padding: 0 30px ;


        }
      }

      .infoItem {
        display: flex;
        margin: 20px 0;
        color: #333333;

        .lable {
          color: #666666;
          font-size: 16px;
          width: 100px;
          text-align: right;
          font-weight: Regular;
        }



      }


      .contactImg {
        width: 38rpx;
        height: 34rpx;
        margin-right: 4px;
      }

      .phoneImg {
        flex: 0 0 auto;
        margin-left: 8px;
        width: 16px;
        height: 16px;
      }


      .popoverImg {
        width: 14px;
        height: 14px;
      }

      .popoverTextDiv {
        width: 130px;
        font-size: 10px;
        font-weight: Regular;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 18px;

      }

      .nut-popover-taro .popover-content--bottom .popover-arrow {
        margin-top: -9rpx;
      }

      .popover-menu {
        border-radius: 18px;
      }
    }



  }
}

