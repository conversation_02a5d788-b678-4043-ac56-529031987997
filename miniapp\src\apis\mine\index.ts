import request
    from '@/utils/request';


// 修改user
export const getInfo = () =>
  request<boolean>({
    url: '/wechat/getInfo',
    method: 'GET'
  });

  // 获取公告列表
export const getNotices = () =>
  request({
    url: '/wechat/getNotices',
    method: 'GET'
  });

      // 获取公告详情
export const getNoticeDetail = (noticeId) =>
  request({
    url: '/wechat/getNoticeDetail',
    method: 'GET',
    params:{
      noticeId
    }
  });
  // 获取投票列表
export const getVotes = (topicId) =>
  request({
    url: '/wechat/getVotes',
    method: 'GET',
    params:{
      topicId
    }
  });



      // 获取投票详情
export const getVoteDetail = (voteId) =>
  request<any>({
    url: '/wechat/getVoteDetail',
    method: 'GET',
    params:{
      voteId
    }
  });
      // 获取投票类型
export const getVoteType = () =>
  request({
    url: '/wechat/voteType',
    method: 'GET'
  });
      // 业主投票
export const postVoteAdd = (data) =>
  request({
    url: '/wechat/voteAdd',
    method: 'POST',
    data
  });
export const getTopics = () =>
  request({
    url: '/wechat/getTopics',
    method: 'GET',
  });
