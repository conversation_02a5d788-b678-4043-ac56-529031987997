<template>
  <view :class="styles.myContainer">
    <navbar title="业主投票列表" />
    <view class="list">
      <nut-cell v-for="item in data.list" :key="item" size="large" class="ticketItem">
        <view class="ticketItemContent">
          <view class="rightContent">
            <view class="line1">{{ item.voteTitle }}</view>
          </view>
        </view>
        <view
          v-if="!item.voteStatus && dayjs().isBefore(data.topicItem.topicEndTime) && dayjs().isAfter(data.topicItem.topicStartTime)"
          class="ticketItemBtns2"
        >
          <view
            v-for="(itemOptionsMap, indexOptionsMap, labOptionsMap) in data.optionsMap"
            :key="indexOptionsMap"
            :style="{
              borderColor: data.btnsColorMap[itemOptionsMap].borderColor,
              backgroundColor: data.btnsColorMap[itemOptionsMap].backgroundColor,
              color: data.btnsColorMap[itemOptionsMap].color,
            }"
            class="tpBtn"
            color="rgb(126,192,80)"
            @tap="userVote(labOptionsMap, item.voteId, itemOptionsMap, item)"
          >
            {{ itemOptionsMap }}
          </view>
        </view>
        <!-- 过期投票tip -->
        <view
          v-else-if="!item.voteStatus && !dayjs().isBefore(data.topicItem.topicEndTime) && dayjs().isAfter(data.topicItem.topicStartTime)"
          class="ticketItemBtns3"
        >
          本轮投票已过期
        </view>
        <!-- 未开始投票tip -->
        <view
          v-else-if="!item.voteStatus && dayjs().isBefore(data.topicItem.topicEndTime) && !dayjs().isAfter(data.topicItem.topicStartTime)"
          class="ticketItemBtns3"
        >
          本轮投票暂未开始
        </view>
        <view v-else class="ticketItemBtns3" @tap="tipAfter">
          已投票：
          <view class="tpBtn"
          :style="{
              borderColor: data.btnsColorMap[item.voteStatus].borderColor,
              backgroundColor: data.btnsColorMap[item.voteStatus].backgroundColor,
              color: data.btnsColorMap[item.voteStatus].color,
            }"
          > {{ item.voteStatus }} </view>
        </view>
      </nut-cell>
    </view>
  </view>

  <!-- toast提示 -->
  <my-toast-components ref="myToast" :duration="2500" />
</template>
<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles
    from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import {
    useAccountStore
} from '@/stores/account';
import {
    getInfo,
    getVotes,
    postVoteAdd
} from '@/apis/mine';
import Taro
    from '@tarojs/taro';
import dayjs
    from 'dayjs';

definePageConfig({ backgroundColor: '#f3f3fe' });

const myToast = ref<any>();
const account = useAccountStore();

const data = reactive({
  list: [] as any,
  html: '',
  showPop: false,
  optionsMap: {},
  btnsColorMap: {
    赞成: {
      borderColor: 'rgb(203,231,184)',
      backgroundColor: 'rgb(242,249,236)',
      color: 'rgb(126,192,80)',
    },
    反对: {
      borderColor: 'rgb(245,211,211)',
      backgroundColor: 'rgb(252,240,240)',
      color: 'rgb(228,116,112)',
    },
    弃权: {
      borderColor: 'rgb(211,211,211)',
      backgroundColor: 'rgb(244,244,244)',
      color: 'rgb(145,147,152)',
    },
    随多数: {
      borderColor: 'rgb(186,215,251)',
      backgroundColor: 'rgb(238,245,254)',
      color: 'rgb(90,156,248)',
    },
    废票: {
      borderColor: 'rgb(241,219,182)',
      backgroundColor: 'rgb(252,246,237)',
      color: 'rgb(220,165,80)',
    },
    已参与未表决: {
      borderColor: 'rgb(241,219,182)',
      backgroundColor: 'rgb(252,246,237)',
      color: 'rgb(220,165,80)',
    },
  },
  topicItem: {} as any,
});

const getUserInfo = async () => {
  const res = await getInfo();
  account.userInfo = res;
};

const getTickList = async () => {
  data.topicItem = Taro.getStorageSync('topicItem');

  console.log(data.topicItem);
  data.list = await getVotes(data.topicItem.topicId);
  data.topicItem.topicOptionsArray.forEach((element) => {
    const label = element.split('-')[0];
    const value = element.split('-')[1];
    data.optionsMap[value] = label;
  });
};

const tipAfter = () => {
  myToast.value.myToastShow({
    icon: 'error',
    title: '已投票，无法二次修改',
    duration: 3000,
  });
};

const userVote = (lab, voteId, txt, item) => {
  if (!dayjs().isBefore(data.topicItem.topicEndTime)) {
    Taro.showModal({
      title: '温馨提示',
      content: '投票已过期',
    });
  } else {
    Taro.showModal({
      title: '温馨提示',
      content: `您的${account.userInfo.houseNum}套房源正在投票，投票后无法二次修改，请确认您的选择。您此次的选项是“${txt}”`,
      success: async (res) => {
        if (res.confirm) {
          await postVoteAdd({
            ownerId: account.userInfo.ownerId,
            voteId: voteId,
            advice: lab,
          });
          myToast.value.myToastShow({
            icon: 'success',
            title: '投票成功',
            duration: 3000,
          });
          getTickList();
        } else if (res.cancel) {
          myToast.value.myToastShow({
            icon: 'error',
            title: '已取消',
            duration: 3000,
          });
        }
      },
    });
  }
};

getTickList();
getUserInfo();
</script>
