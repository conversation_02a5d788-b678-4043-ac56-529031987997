page {
  background: #f5f5f9;
}
.myContainer {
  width: 100vw;
  height: 100vh;

  :global {
    .nut-noticebar {
      width: 100%;
      height: 40px;
      .nut-noticebar__page {
        padding: 0;
      }
      .left-icon {
        display: none;
      }
      .right-icon {
        display: none;
      }
    }
    .menuContent {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 10px;
      margin: 20px 0 ;


      .item {
        flex: 1;
        margin-left: 8px;
        padding: 10px;
        border-radius: 10px;
        background-color: rgb(175, 198, 200);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 28px;
        font-weight: 600;
        color: rgb(245, 248, 251);
        height: 100px;
        // display: flex;
        // flex-direction: row;
        image {
          width: 70px;
          height: 70px;
          // margin: 10px;
        }
      }

      .vistor {
        margin-left: 0;
        background-image: url('../../assets/images/lpt/vistor.jpg');
        background-position: center;
        background-size: 100%;
      }

      .photographer {
        margin-left: 0;
        background-color: rgb(241, 197, 194);
        background-image: url('../../assets/images/lpt/photographer.jpg');
        background-position: center;
        background-size: 100%;
      }

      .questionnaire {
        margin-left: 0;
        background-color: rgb(175, 198, 200);
        background-image: url('../../assets/images/lpt/vistor.jpg');
        background-position: center;
        background-size: 100%;
      }
    }
    .menu{
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-around;
      // align-items: flex-start;
      justify-content: center;
      align-items: center;
      .menu-item{
        width: 150px;
        height: 150px;
        margin: 10px;
        border: 1px  #333 ;
        border-style: dashed;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        border-radius: 50%;
        position: relative;
        overflow: hidden;

        .title{
          font-size: 20px;
          font-weight: 500;
          color: #333;
          opacity: .9;
        }
        .bgImg{
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0px;
          z-index: -1;
          opacity: .2;
        }
      }
    }
  }
}
