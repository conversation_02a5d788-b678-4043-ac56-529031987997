.mpm-scroll-view {
  width: 100%;
  height: 100%;
  &-refresher {
    width: 100%;
    display: flex;
    height: 60PX; // 固定高度
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999999;
    line-height: 12px;
    overflow: hidden;
    position: absolute;
    top: -60PX;
    left: 0;
    &.unset {
      .nut-icon-loading, .nut-icon-loading1 {
        animation: unset;
      }
    }
    .nut-icon {
      margin-bottom: 10px;
    }
  }

  &-list {
    width: 100%;
    height: 100%;
  }

  &-footer {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 12px;
    color: #999999;
    &.float {
      position: absolute;
    }
    .nut-icon {
      margin-right: 8px;
    }
  }
}