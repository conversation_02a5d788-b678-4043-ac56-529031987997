.mpmChoosePop {
  width: 100%;
  :global {
    .mpmChoosePopupMain{
      display: flex;
      flex-direction: column;
      height: 100%;
      .mpmChoosePopupMainTitle {
       position: relative;
       background: #fff;
        .mpmChoosePopupMainHead {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          padding: 20px 20px 16px 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex: 1;
        }
        .mpmChoosePopupMainClose {
          position: absolute;
          top: 16px;
          right: 0;
          width: 30px;
          height: 30px;
          margin-right: 20px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
      }
      .mpmChoosePopupSearch{
        background-color: #fff;
        .nut-searchbar {
          padding: 8px 16px;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          background-color:  transparent;
          .nut-searchbar__search-input{
            box-shadow: none !important;
            background-color: #F5F5F9 !important;
          }
          .nut-searchbar__iptleft-search-icon {
            width: 16px !important;
            height: 16px !important;
          }
          .nut-searchbar__input-bar{
            width: 140%;
          }
        }
      }
      .searchChooseMain{
        flex: 1;
        // overflow: hidden;
        overflow: scroll;
        .sv{
          height: 100%;
        }
        .nut-empty{
          width: 100%;
          justify-content: flex-start;
        }
        .nut-list-item{
          margin-bottom: 0px;
        }
        .searchChooseItem{
          display: flex;
          padding: 14px;
          align-items: center;
          background-color: #fff;
          margin-bottom: 2px;
          .mpm_check {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #7468F2;
            &__checked {
              background: url(https://panshi-on.oss-cn-hangzhou.aliyuncs.com/yunxiaoding-mini/icon-checked.png) no-repeat center center;
              background-size: 18px 18px;
            }
          }
          .info{
            flex: 1;
            align-self: baseline;
            font-size: 16px;
            font-weight: 400;
            margin-left: 14px;
          }

        }

      }
      .choosePopFooter {
        box-shadow: 0px -5px 8px 0px rgba(237, 237, 237, 0.5);
        box-sizing: border-box;
        padding: 8px 32px;
        background-color: #fff;
        padding-bottom:calc(env(safe-area-inset-bottom) + 20px) ;
        display: flex;
        align-items: center;
        .next {
          font-size: 16px;
          height: 39px;
          flex: 1;
        }

      }
    }
  }
}
