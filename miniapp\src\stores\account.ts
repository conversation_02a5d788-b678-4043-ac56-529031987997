/* eslint-disable @typescript-eslint/consistent-type-assertions */
import type {
    IMemo
} from '@/apis/memo/model';
import type {
    IResult
} from '@/components/selectMedia';
import {
    defineStore
} from 'pinia';
import type {
    IListDataItem
} from 'types/global';

interface IState {
  templeChoosePostList: IResult[]; // 上传选择的临时资源
  editMemoData: IMemo; // 前端缓存修改备忘录的内容
  memoDataList: IMemo[]; // 前端缓存备忘录的数据（列表+详情）
  userInfo: any; // 用户信息
  mainMenuList: IListDataItem[]; // 主菜单
  cxkMenuList: IListDataItem[]; // cxk主菜单
}

export const useAccountStore = defineStore('account', {
  // eslint-disable-next-line max-lines-per-function
  state: (): IState => ({
    userInfo: {},
    mainMenuList: [
      // 业主投票入口
      {
        title: 'ticket',
        Ctitle: '业主投票',
        router: '/pages/ticket/index',
      },
      {
        title: 'announcement',
        Ctitle: '通知公告',
        router: '/pages/announcement/index',
      },


    ],
    cxkMenuList: [],
    editMemoData: {} as IMemo,
    templeChoosePostList: [],
    memoDataList: [],
  }),
  actions: {

  },
});
