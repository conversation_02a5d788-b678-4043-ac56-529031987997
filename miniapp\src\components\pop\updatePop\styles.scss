.updatePopDiv {
  :global {
    .my-pop {
      border-radius: 8px;
      width: 574rpx;

      // height: 153px;
      // .nutui-popup__content-wrapper {
        display: flex;
        flex-direction: column;

        .content {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 48rpx 28rpx 2rpx 28rpx;

          .title {
            margin-bottom: 24rpx;
          }

          .nut-input {
            width: 320rpx;
            height: 64rpx;
            padding: 0;
            background-color: #F5F5F9;
            border-radius: 4px;

            .nut-input-value {
              display: flex;
              justify-content: center;
              align-items: center;
              .nut-input-inner {
                height: 100%;
                font-size: 28rpx;
                .input-text {
                  text-align: center !important;
                  margin: 0 auto;
                }
              }
            }
          }

          .contentText {
            color: #999999;
            line-height: 44rpx;
            font-size: 32rpx;
            text-align: center;
            font-weight: Regular;
            letter-spacing: 2rpx;

          }
          .error-text{
            text-align: center;
            color: red;
            font-size: 12px;

          }
        }

        .footer {
          height: 88rpx;
          display: flex;
          margin-top: 48rpx;

          .cancel {
            flex: 1;
            color: #999999;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 0.5px solid #DDDDDD;
            border-right: 0.5px solid #DDDDDD;
            font-weight: 400;
          }

          .confirm {
            border-top: 0.5px solid #DDDDDD;
            flex: 1;
            color: #333;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;

          }
        }
      // }

    }
  }
}
