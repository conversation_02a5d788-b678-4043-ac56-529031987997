import {
    ApiError,
    createRequest
} from './customRequest';
import Taro
    from '@tarojs/taro';
import {
    baseApi
} from './env';
import {
    debounce
} from 'lodash';

const log = (message: string, style?: string) => {
  if (process.env.NODE_ENV !== 'production') return;
  console.log(`%c ${message}`, style); // eslint-disable-line no-console
};

export let baseURL = Taro.getStorageSync('tp-dev-baseURL') || baseApi;

log(`baseAPI: ${baseURL}`, 'color: cyan');

/*****************************************************************************************************************
 *********************************************** 权限相关 ***********************************************
 ***************************************************************************************************************** **/
export const tokenKey = `TP-token-${process.env.BUILD_ENV}`;
export const tokenUtil = {
  set: (token) => Taro.setStorageSync(tokenKey, token),
  get: (): string => Taro.getStorageSync(tokenKey),
  clear: () => Taro.removeStorage({ key: tokenKey }),
};

//  退出登录逻辑
export const unauthorized = debounce(() => {
  requestInstance.setConfig({ header: { Authorization: ''}});
   tokenUtil.clear();
 }, 3000, { leading: true, trailing: false });

const systemInfo = Taro.getSystemInfoSync();

// eslint-disable-next-line no-console
const commonError = new Map<number, string>([
  [404, '404 Not Found'],
  [401, '401 Unanthorized'],
  [500, '500 Service Exception'],
  [502, '502 Service is Not Ready'],
]);

/**
 * create a global request instance
 * ****/
const requestInstance = createRequest({
  baseURL,
  header: {
    // @ts-ignore
    ...(process.env.buildInfo || {}),
    'content-type': 'application/json',
    Authorization:  tokenUtil.get()?`Bearer ${ tokenUtil.get()}`:'',
    appName: 'TP-mini',
    platform: systemInfo.platform,
    model: systemInfo.model,
    system: systemInfo.system,
    wxVersion: systemInfo.version,
  },
  dataType: 'json',
  enableHttp2: true,
  enableQuic: true,
  enableCache: true,
});

export const setBaseURL = (url) => {
  requestInstance.setConfig({ baseURL: url });
  baseURL = url;
};

requestInstance.interceptors.response.use(
  async (response, config) => {

    if (response.statusCode === 200) {

      const responseData = response.data;
      // 阿里云上的文件特殊处理
      if(response?.header?.Server==='AliyunOSS'){
        return response.data ; // 正确返回
      }

      if ((responseData.code as unknown as number)=== 401) {
        // token过期，清除token
        unauthorized();
        Taro.switchTab({ url: '/pages/login/index' });
        return responseData.data || (responseData.msg as any); // 正确返回
      }
      // 注册状态查询接口特殊处理
      if ((responseData.code as unknown as number) === 501) {
        return responseData.data || (responseData.msg as any); // 正确返回
      }

      if ((responseData.code as unknown as number) !== 200) {
        console.log(999999,config);
        log(
          `${config.method} ${config.url
            .replace(`${config.baseURL}/`, '')
            .replace(/\?.+/g, '')}  ERROR \n参数: ${
            config.method === 'GET'
              ? JSON.stringify(config.params)
              : JSON.stringify(config.data)
          } \ntoken: ${config.header.token} \n返回: ${commonError.get(
            response.statusCode
          )}`,
          'color:red'
        ); // eslint-disable-line max-len
        throw new ApiError(responseData.msg, responseData.code);
      }

      return responseData.data || responseData.rows||(responseData.msg as any); // 正确返回
    } else if (response.statusCode === 403) {

      // token过期，清除token
      unauthorized();
      Taro.switchTab({ url: '/pages/login/index' });

    } else {

      log(
        `${config.method} ${config.url
          .replace(`${config.baseURL}/`, '')
          .replace(/\?.+/g, '')}  ERROR \n参数: ${
          config.method === 'GET'
            ? JSON.stringify(config.params)
            : JSON.stringify(config.data)
        } \ntoken: ${config.header.token} \n返回: ${commonError.get(
          response.statusCode
        )}`,
        'color:red'
      ); // eslint-disable-line max-len
      throw new Error(commonError.get(response.statusCode) || '请求异常');
    }
  },
  (error: Error & { errMsg?: string }, { showErrorMsg }) => {
    if (error.errMsg?.startsWith('request:fail')) {
      log(
        `${error.errMsg}  ${baseURL} 服务不可用`,
        'background: red; color: #fff; font-size: 24px'
      );
      return error;
    }
    if (showErrorMsg) {
      Taro.showToast({
        title: error.message,
        icon: error.message.length > 6 ? 'none' : 'error',
        duration: 2000,
      });
    }
    return error;
  }
);

export default requestInstance;
