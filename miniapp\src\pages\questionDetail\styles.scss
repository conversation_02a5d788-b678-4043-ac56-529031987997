page {
  background: #f5f5f9;
}

.myContainer {
  min-height: 100vh;
  background-color: #f5f5f9;

  :global {
    // 加载状态
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 300px;
      
      .loading-spinner {
        width: 30px;
        height: 30px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 12px;
      }
      
      .loading-text {
        color: #666;
        font-size: 14px;
      }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .question-detail {
      padding: 0;
      padding-bottom: 80px;
    }

    // 问卷头部
    .question-header {
      background: white;
      padding: 16px;
      margin: 16px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      .question-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 12px;
        text-align: left;
        line-height: 1.4;
      }
      
      .question-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 16px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #1890ff;
      }
      
      .question-info {
        .info-item {
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .info-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
            display: inline-block;
            font-weight: 500;
          }
          
          .info-value {
            font-size: 14px;
            color: #333;
            margin-left: 8px;
          }
        }
      }
    }

    // 问题项目
    .question-items {
      padding: 16px;
      
      .question-item-card {
        background: white;
        border-radius: 8px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        
        .item-header {
          padding: 16px;
          border-bottom: 1px solid #f0f0f0;
          background: #fafafa;
          
          .item-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
          }
        }
        
        // 选择题选项
        .item-options {
          padding: 16px;
          
          .option-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            
            &.selected {
              .option-radio,
              .option-checkbox {
                color: #1890ff;
              }
              
              .option-text {
                color: #1890ff;
                font-weight: 500;
              }
            }
            
            .option-radio,
            .option-checkbox {
              margin-right: 12px;
              transform: scale(1.2);
            }
            
            .option-content {
              flex: 1;
              
              .option-text {
                font-size: 15px;
                color: #333;
                line-height: 1.4;
              }
            }
          }
        }
        
        // 填空题输入
        .item-input {
          padding: 16px;
          
          .answer-textarea {
            width: calc(100% - 16px); // 减去一些宽度，避免与右边界贴合
            min-height: 80px;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background: #fff;
            resize: none;
            font-family: inherit;
            box-sizing: border-box; // 确保padding包含在宽度内
            
            &:focus {
              border-color: #1890ff;
              outline: none;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
            
            &::placeholder {
              color: #bfbfbf;
            }
          }
        }
      }
    }

    // 签名提示区域
    .signature-notice {
      background: white;
      margin: 16px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .signature-notice-content {
        display: flex;
        align-items: center;
        padding: 16px;
        background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
        border-left: 4px solid #faad14;
        
        .notice-icon {
          font-size: 24px;
          margin-right: 12px;
        }
        
        .notice-text {
          flex: 1;
          
          .notice-title {
            font-size: 16px;
            color: #333;
            font-weight: bold;
            margin-bottom: 4px;
          }
          
          .notice-desc {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
          }
        }
        
        .signature-status {
          .signed-status {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 16px;
            
            .status-icon {
              margin-right: 4px;
              font-size: 12px;
            }
            
            .status-text {
              font-size: 12px;
              color: #52c41a;
              font-weight: 500;
            }
          }
          
          .unsigned-status {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 16px;
            
            .status-icon {
              margin-right: 4px;
              font-size: 12px;
            }
            
            .status-text {
              font-size: 12px;
              color: #faad14;
              font-weight: 500;
            }
          }
        }
      }
      
      .signature-preview {
        padding: 16px;
        border-top: 1px solid #f0f0f0;
        text-align: center;
        
        .preview-label {
          font-size: 13px;
          color: #666;
          margin-bottom: 12px;
        }
        
        .signature-img {
          max-width: 200px;
          max-height: 80px;
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          margin-bottom: 12px;
        }
        
        .re-sign-btn {
          padding: 6px 16px;
          background: #f5f5f5;
          color: #666;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          font-size: 13px;
        }
      }
    }

    // 提交区域
    .submit-section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: white;
      padding: 12px 16px;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
      
      .submit-btn {
        width: 100%;
        height: 44px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        
        &.disabled {
          background: #d9d9d9;
          color: #fff;
        }
      }
    }

    // 错误状态
    .error-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      
      .error-icon {
        font-size: 40px;
        margin-bottom: 12px;
      }
      
      .error-text {
        color: #666;
        font-size: 14px;
        margin-bottom: 16px;
      }
      
      .retry-btn {
        padding: 8px 20px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
      }
    }

    // 签名弹窗
    .signature-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      
      .modal-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
      }
      
      .signature-popup {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-radius: 12px 12px 0 0;
        max-height: 70vh;
        animation: slideUp 0.3s ease-out;
        
        .popup-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #f0f0f0;
          
          .popup-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            flex: 1;
            text-align: center;
          }
          
          .close-btn {
            font-size: 18px;
            color: #999;
            padding: 4px;
          }
        }
        
        .signature-content {
          padding: 20px;
          
          .signature-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
          }
          
          .signature-canvas-container {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
            position: relative;
            margin-bottom: 20px;
            
            .signature-canvas {
              width: 100%;
              height: 120px;
              background: white;
              border-radius: 6px;
            }
            
            .signature-placeholder {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: #bfbfbf;
              font-size: 14px;
              pointer-events: none;
            }
          }
          
          .signature-buttons {
            display: flex;
            gap: 12px;
            
            .clear-btn {
              flex: 1;
              height: 44px;
              background: #f5f5f5;
              color: #666;
              border: 1px solid #d9d9d9;
              border-radius: 6px;
              font-size: 16px;
              font-weight: 500;
            }
            
            .confirm-btn {
              flex: 1;
              height: 44px;
              background: #1890ff;
              color: white;
              border: none;
              border-radius: 6px;
              font-size: 16px;
              font-weight: 500;
            }
          }
          
          .modal-submit-btn {
            width: 100%;
            height: 44px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }
      to {
        transform: translateY(0);
      }
    }
  }
}