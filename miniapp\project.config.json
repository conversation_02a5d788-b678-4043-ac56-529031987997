{"miniprogramRoot": "dist/", "projectname": "TP-mini", "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "appid": "wx6cf82d7899fd6af2", "setting": {"urlCheck": false, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "minifyWXSS": false, "minifyWXML": false, "uglifyFileName": false, "uploadWithSourceMap": false, "packNpmManually": false, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false}, "compileType": "miniprogram"}