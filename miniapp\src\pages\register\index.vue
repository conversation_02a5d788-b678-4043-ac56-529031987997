<template>
  <view :class="styles.myContainer">
    <navbar title="注册" background-color="transparent" />
    <!-- <nut-watermark :gap-x="20" font-color="rgba(0, 0, 0, .1)" :z-index="1" content="家园议事厅" /> -->

    <!-- <nut-noticebar right-icon="circle-close" background="#F1EFFD" color="#333" :speed="35">
      家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、
    </nut-noticebar> -->

    <!-- logo -->
    <view class="head">
      <image src="@/assets/images/lpt/logo.jpg" />
    </view>

    <!-- 页面文案 -->
    <view class="tip">
      <view class="t1"> 业主投票小助手是帮助业主提供投票选择物业服务的工具。 </view>
      <view class="t2"> 自主选择，快速投票! </view>
    </view>

    <!-- 绑定信息 -->
    <view v-if="data.isBinding" class="info">
      <view class="t3"> 本助手需要申请使用，绑定下方信息立即申请 </view>

      <nut-form>
        <nut-form-item label="小区">
          <view
            class="content"
            @tap="
              () => {
                data.sheetShow = true;
              }
            "
          >
            {{ data.xqVal.housingName || '点击选择小区' }}
          </view>
        </nut-form-item>
        <nut-form-item label="业主姓名">
          <!-- <view class="content" @tap="nicknamePop.visible = true">
            {{ data.ownerName || '请输入业主姓名' }}
          </view> -->
          <nut-input v-model="data.ownerName" class="content" placeholder="请输入业主姓名" type="text" />
        </nut-form-item>

        <nut-form-item label="手机号">
          <button class="content buttonWidthNum" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
            {{ !!data.ownerPhone ? data.ownerPhone : '点击绑定手机号' }}
          </button>
        </nut-form-item>

        <nut-form-item label="楼栋">
          <nut-input v-model="data.building" class="content" placeholder="请输入楼栋" type="text" />
        </nut-form-item>
        <nut-form-item label="单元">
          <nut-input v-model="data.cell" class="content" placeholder="请输入单元" type="text" />
        </nut-form-item>
        <nut-form-item label="房号">
          <nut-input v-model="data.room" class="content" placeholder="请输入房号" type="text" />
        </nut-form-item>
        <nut-form-item label="面积" class="area"> <nut-input-number v-model="data.acreage" input-width="80" step="0.01" decimal-places="2" class="content" />m² </nut-form-item>
        <nut-form-item label="房产证正面">
          <nut-uploader
            multiple
            maximum="1"
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessZ"
          >
          </nut-uploader>
        </nut-form-item>
        <nut-form-item label="身份证正面">
          <nut-uploader
            multiple
            maximum="1"
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessSZ"
          >
          </nut-uploader>
        </nut-form-item>
        <nut-form-item label="身份证反面">
          <nut-uploader
            multiple
            maximum="1"
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessSF"
          >
          </nut-uploader>
        </nut-form-item>
      </nut-form>
      <view class="btns">
        <nut-button block class="sqBtn" @click="handleClickBtn"> 确认申请 </nut-button>
      </view>
    </view>

    <view v-else >
      <view v-if="!data.isPass"  class="binding">
        <view class="txt"> 已提交申请，点击刷新试试~ </view>
        <nut-button block type="primary" class="fresh" @click="handleClickFreshBtn"> 刷新 </nut-button>
      </view>
      <view v-else  class="binding">
        <view class="txt"> 已通过审核，请返回登录~ </view>
      </view>
    </view>

     <!-- 选择小区  -->
     <searchChoosePop
      v-model:popChooseVisible="data.sheetShow"
      title="选择小区"
      searchPlaceholder="搜索小区名称"
      :choose-list="[data.xqVal]"
      :range="data.xqList"
      range-name="housingName"
      range-value="housingId"
      @choose-confirm="handleConfimPick"
    />
    <!-- 选择小区  -->
    <!-- <nut-popup v-model:visible="data.sheetShow" position="bottom">
      <nut-picker
        :field-names="{
          text: 'housingName',
          value: 'housingId',
        }"
        :columns="data.xqList"
        title="请选择小区"
        @confirm="handleConfimPick"
        @cancel="data.sheetShow = false"
      />
    </nut-popup> -->
  </view>
  <!-- toast提示 -->
  <my-toast-components ref="myToast" :duration="2500" />

  <nickname-robber v-model:visible="nicknamePop.visible" @change="onChangeNickname" />
</template>
<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles
    from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import NicknameRobber
    from '@/components/nicknameRobber';
import Taro
    from '@tarojs/taro';
import {
    getHousingList,
    getPhone,
    getRegisterStatus,
    postRegister
} from '@/apis/login';
import {
    debounce
} from 'lodash';
import searchChoosePop
    from '@/components/searchChoosePop/index.vue';

definePageConfig({ backgroundColor: '#f3f3fe' });

const myToast = ref<any>();

const nicknamePop = reactive({
  visible: false,
});

const data = reactive({
  xqList: [],
  sheetShow: false,
  isBinding: true,
  xqVal: {} as any,
  ownerName: '',
  building: '',
  cell: '',
  room: '',
  acreage: '',
  homeLicenseZ: '',
  idCardZ:'',
  idCardF:'',
  ownerPhone: '',
  isPass: false,
});

const getPhoneNumber = async (e) => {
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const res = await getPhone({
      jsCode: e.detail.code,
    });
    data.ownerPhone = res;
    console.log(99999, res);
  } else {
    myToast.value.myToastShow({
      icon: 'error',
      title: '您点击了拒绝授权，将会影响您的功能使用',
      duration: 2000,
    });
  }
};

const handleClickBtn = async () => {
  const wxCodeRes = await Taro.login();

  await postRegister({
    housingId: data.xqVal.housingId,
    ownerName: data.ownerName,
    ownerPhone: data.ownerPhone,
    acreage: data.acreage,
    building: data.building,
    cell: data.cell,
    room: data.room,
    homeLicenseZ: data.homeLicenseZ,
    idCardZ: data.idCardZ,
    idCardF: data.idCardF,
    jsCode: wxCodeRes.code,
  });
  // 验证三条内容已填写
  data.isBinding = false;
};

const onChangeNickname = async (nickName: string) => {
  if (!nickName.trim()) return;
  // 更新userinfo
  data.ownerName = nickName;
  myToast.value.myToastShow({
    icon: 'success',
    title: '修改成功',
    duration: 3000,
  });
};

const getStatus = debounce(
  async () => {
    const res = await getRegisterStatus();
    if (res === '用户不存在') {
      data.isBinding = true;
    } else if (res.audit === '0') {
      data.isBinding = false;
    } else if (res.audit === '1') {
      // switchTab({ url: '/pages/login/index' });
      data.isBinding = false;
      data.isPass = true;
      myToast.value.myToastShow({
        icon: 'success',
        title: '您已通过审核，请前往登录~',
        duration: 3000,
      });
    }
    // 刷新状态成功去 登录页面，刷新状态失败
  },
  3000,
  { leading: true, trailing: false },
);

const handleClickFreshBtn = async () => {
  await getStatus();
  myToast.value.myToastShow({
    icon: 'success',
    title: '刷新成功',
    duration: 3000,
  });
  // getStatus();
};

// 获取小区列表
const getXQList = async () => {
  data.xqList = await getHousingList();
};

const handleConfimPick = (chooseList) => {
  data.xqVal = chooseList[0];;
  data.sheetShow = false;
};

const uploadSuccessZ = (parpams) => {
  if (JSON.parse(parpams.data.data).code === 200) {
    data.homeLicenseZ = JSON.parse(parpams.data.data).data.ossId;
  }
};
const uploadSuccessSZ = (parpams) => {
  if (JSON.parse(parpams.data.data).code === 200) {
    data.idCardZ = JSON.parse(parpams.data.data).data.ossId;
  }
};
const uploadSuccessSF = (parpams) => {
  if (JSON.parse(parpams.data.data).code === 200) {
    data.idCardF = JSON.parse(parpams.data.data).data.ossId;
  }
};

// 获取小区列表
getXQList();
// 获取账号注册状态
getStatus();
</script>
