.popup {
  :global {
    .nut-popup {
      width: 100%;
      height: 100%;
      background-color: transparent;
      overflow: hidden;
    }
    .nut-ani-container {
      text-align: center;
    }
    .placeholder-class {
      color: transparent;
      background: transparent;
      font-size: 0;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  position: relative;
  .abs {
    position: absolute;
    left: 0;
    bottom: 70px;
    display: flex;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    color: #F0E211;
    .main{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 33px;
        height: 51px;
        margin-top: 18px;
      }
    }
    
  }
  .input {
    // background-color: rgba($color: #bd1616, $alpha: 0.4);
    position: absolute;
    height: 100%;
    width: 400%;
    left: -300%;
  }
}