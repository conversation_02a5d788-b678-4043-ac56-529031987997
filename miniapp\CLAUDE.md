# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Taro-based WeChat mini-program built with Vue 3, TypeScript, and Pinia for state management. The project is a housing management mini-app with features for owner voting, announcements, questionnaires, and user management.

## Development Commands

### Build Commands
- `npm run start test` - Start development server for test environment with watch mode
- `npm run start prod` - Start development server for production environment with watch mode  
- `npm run build:test` - Build for test environment
- `npm run build:prod` - Build for production environment

### Code Quality
- ESLint is configured with `eslint-config-alloy`, `eslint-config-prettier`, and Vue-specific rules
- Prettier is configured for code formatting
- `lint-staged` runs ESLint on staged files during git commits
- <PERSON><PERSON> is set up for git hooks

## Architecture

### Framework & Core Dependencies
- **Taro 3.6.23** - Multi-platform framework for WeChat mini-programs
- **Vue 3.4.21** - Frontend framework with Composition API
- **TypeScript 5.3.3** - Type system
- **Pinia 2.1.7** - State management
- **NutUI Taro 4.3.0** - UI component library
- **Less** - CSS preprocessor

### Project Structure

```
src/
├── apis/           # API modules organized by feature
│   ├── login/      # Authentication APIs
│   ├── memo/       # Memo/note APIs
│   ├── mine/       # User profile APIs
│   ├── question/   # Questionnaire APIs
│   └── speedTime/  # Time-related APIs
├── assets/         # Static assets
│   ├── images/     # Image resources
│   └── styles/     # Global styles (SCSS)
├── components/     # Reusable components
├── pages/          # Page components (Taro pages)
├── stores/         # Pinia stores
└── utils/          # Utility functions
```

### Configuration
- **Multi-environment builds**: Test and production environments via `--buildEnv` flag
- **Path aliases**: `@/apis`, `@/assets`, `@/components`, `@/stores`, `@/utils` configured
- **CSS Modules**: Enabled with custom naming pattern `[name]__[local]___[hash:base64:5]`
- **Design Width**: 375px with 2x device ratio
- **Component Auto-import**: NutUI components auto-imported via unplugin-vue-components

### State Management
- Uses Pinia with TypeScript
- Main store: `useAccountStore` for user info and menu configuration
- Stores located in `src/stores/`

### Styling
- Global SCSS files auto-imported: `custom_theme.scss`, `common.scss`, `flexible.scss`
- NutUI theme variables included
- CSS Modules for component-scoped styles
- pxtransform for responsive design (excludes `nut-` prefixed selectors)

### API Architecture
- Custom request wrapper in `src/utils/customRequest/`
- APIs organized by feature domains
- TypeScript models defined for each API module

### Pages & Routing
- **Main pages**: index, login, menu (tab pages)
- **Subpackages**: webViewPage, ticket, register, ticketDetail, announcement, mine, question
- **Entry point**: `pages/index/index`
- **Custom navigation**: `navigationStyle: 'custom'`
- **Custom tabBar**: Configured but using custom implementation

## Development Notes

### Icon System
- Uses Alibaba Iconfont with auto-generation via `taro3-vue3-iconfont`
- Font URL: `//at.alicdn.com/t/c/font_3800888_v2gj6229j2k.js`
- Generated components in `src/components/alIconfont/`

### Build Environment Variables
- `BUILD_ENV`: 'test' or 'prod' 
- `FIX_ENV`: WeChat fix environment flag
- Access via `process.env.BUILD_ENV` and `process.env.FIX_ENV`

### Component Libraries
- **NutUI**: Primary UI library for Taro
- **@fishui/taro-vue**: Additional Taro components
- **@lucky-canvas/taro**: Canvas components for interactive features

### Development Workflow
1. Use `npm run start test` for development
2. ESLint and Prettier are enforced via git hooks
3. Build artifacts output to `dist/` directory
4. WeChat mini-program specific configurations in `project.config.json`