<!-- eslint-disable vue/prefer-true-attribute-shorthand -->
<template>
  <view :class="styles.mpmChoosePop">
    <nut-popup
      :visible="props.popChooseVisible"
      :style="{
        height: '550px',
        background: 'linear-gradient(180deg, #FBFBFD 0%, #F5F5F9 100%)',
      }"
      position="bottom"
      round
      class="mpmChoosePopup"
      :catch-move="false"
      :close-on-click-overlay="false"
    >
      <view v-if="props.popChooseVisible" class="mpmChoosePopupMain">
        <view class="mpmChoosePopupMainTitle">
          <view class="mpmChoosePopupMainHead">{{ props.title }}</view>
          <view class="mpmChoosePopupMainClose" @tap="handleClose">
            <alIconfont name="icon-chacha" size="12" fill="#666666" />
          </view>
        </view>
        <view class="mpmChoosePopupSearch">
          <nut-searchbar v-model="data.searchValues" max-length="16" :placeholder="props.searchPlaceholder">
            <template v-slot:leftin>
              <IconFont name="https://panshi-on.meipingmi.com.cn/yunxiaoding-mini/<EMAIL>" size="16" />
            </template>
          </nut-searchbar>
        </view>
        <view class="searchChooseMain">

          <scroll-view v-if="filterList.length > 0" scroll-y="true" :style="{ height }">
            <view v-for="item in filterList" :key="item[rangeValue]">
              <view class="searchChooseItem" @tap="toggleChecked(item)">
                <view
                  :class="[
                    'mpm_check',
                    {
                      mpm_check__checked: item.check,
                    },
                  ]"
                />
                <view class="info">
                  <view class="goodsname">{{ item[props.rangeName] }}</view>
                </view>
              </view>
            </view>
          </scroll-view>

          <nut-empty v-else  />
        </view>
        <view class="choosePopFooter">
          <nut-button type="primary" class="next" @tap="chooseConfirm">确认选择</nut-button>
        </view>
      </view>
    </nut-popup>
  </view>
</template>

<script lang="ts" setup>
import {
    computed,
    reactive,
    watch
} from 'vue';
// @ts-ignore
import styles
    from './styles.scss';

interface IProps {
  title: string;
  searchPlaceholder: string;
  popChooseVisible: boolean;
  chooseList: any[];
  range: any[];
  rangeName: string;
  rangeValue: string;
}

const props = withDefaults(defineProps<IProps>(), {
  title: '',
  rangeName: 'name',
  rangeValue: 'id',
  searchPlaceholder: '',
  popChooseVisible: false,
  chooseList: () => [],
  range: () => [],
});

const emit = defineEmits(['update:popChooseVisible', 'chooseConfirm']);

const data = reactive({
  searchValues: '',
});

const height = computed(() => 'calc( 550px - 48px - 58px - 50px -  env(safe-area-inset-bottom) )');


const filterList = computed(() => {
  if (!data.searchValues) return props.range;
  return props.range.filter((v) => v[props.rangeName]?.includes(data.searchValues));
});

const handleClose = () => {
  emit('update:popChooseVisible', false);
};

const chooseConfirm = () => {
  emit(
    'chooseConfirm',
    props.range.filter((item) => item.check === true),
  );
  handleClose();
};

const toggleChecked = (nowChooseItem) => {
  props.range.forEach((item) => {
    item.check = item[props.rangeValue] === nowChooseItem[props.rangeValue];
  });

};


const initCheck = () => {
  data.searchValues = '';
  if (props.chooseList.length <= 0) return;
  // 单选默认选中
  props.range.forEach((item) => {
    item.check = item[props.rangeValue] === props.chooseList[0][props.rangeValue];
  });
};

watch(
  () => props.popChooseVisible,
  (val) => {
    val && initCheck();
  },
  { immediate: true },
);
</script>
