{
  "compilerOptions": {
    "target": "ES2019",
    "module": "ESNext",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "baseUrl": ".",
    "types": ["@types/wechat-miniprogram", "node"],
    "paths": {
      "@/apis/*": ["./src/apis/*"],
      "@/assets/*": ["./src/assets/*"],
      "@/components/*": ["./src/components/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/utils/*": ["./src/utils/*"],
      "@root/project.config.json": ["./project.config.json"],
      "@root/package.json": ["./package.json"],
    },
    "rootDir": ".",
    "jsx": "preserve",
    "allowJs": true,
    "resolveJsonModule": true,
    "typeRoots": [
      "node_modules/@types"
    ]
  },
  "include": ["./src", "./types"],
  "compileOnSave": false
}
