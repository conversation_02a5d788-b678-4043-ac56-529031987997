.myContainer {
  // background-color: #f5f5f9;
  position: relative;

  :global {
    width: 100%;
    height: 100vh;
    background: transparent;
    .mpmChoosePopupMain{
      display: flex;
      flex-direction: column;
      height: 100%;
      .mpmChoosePopupMainTitle {
       position: relative;
       background: #fff;
        .mpmChoosePopupMainHead {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          margin: 20px 50px 16px 50px;
          flex: 1;
          white-space: nowrap;
          overflow: scroll;
          display: flex;
          align-items: center;
          justify-content: center;
          .scrollbar{
            width: 0;
            height: 0;
            color: transparent
           }
        }
        .mpmChoosePopupMainClose {
          position: absolute;
          top: 16px;
          right: 0;
          width: 30px;
          height: 30px;
          margin-right: 20px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
      }
      .mpmChoosePopupSearch{
        background-color: #fff;
        .nut-searchbar {
          padding: 8px 16px;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          background-color:  transparent;
          .nut-searchbar__search-input{
            box-shadow: none !important;
            background-color: #F5F5F9 !important;
          }
          .nut-searchbar__iptleft-search-icon {
            width: 16px !important;
            height: 16px !important;
          }
          .nut-searchbar__input-bar{
            width: 140%;
          }
        }
      }
      .searchChooseMain{
        flex: 1;
        // overflow: hidden;
        overflow: scroll;
        .sv{
          height: 100%;
        }
        .nut-empty{
          width: 100%;
          justify-content: flex-start;
        }
        .nut-list-item{
          margin-bottom: 0px;
        }

      }
      .popContent {
        padding: 0 20px;
        flex: 1;
        overflow: scroll;
      }
      .choosePopFooter {
        box-shadow: 0px -5px 8px 0px rgba(237, 237, 237, 0.5);
        box-sizing: border-box;
        padding: 8px 32px;
        background-color: #fff;
        padding-bottom:calc(env(safe-area-inset-bottom) + 10px) ;
        display: flex;
        align-items: center;
        .next {
          font-size: 16px;
          height: 39px;
          flex: 1;
        }

      }
    }



    .announcementList {
      padding: 0 10px;

      .announcementItem {
        border: 1px solid #f0f0fa ;

        display: flex;
        align-items: flex-start;
        font-size: 16px;
        font-weight: 400;
        color: #333;

        .leftContent {
          width: 60px;
          margin-right: 12px;
          margin-top: 16px;
          image {
            width: 100%;
            height: 100%;
          }
        }

        .rightContent {

          .line {
            margin-bottom: 5px;
            font-size: 14px;
            text{
              font-weight: 600;
              font-size: 16px;

            }

          }


          .detail {
            color: #7468F2;
            font-weight: 500;
          }
        }
      }

    }

  }
}
