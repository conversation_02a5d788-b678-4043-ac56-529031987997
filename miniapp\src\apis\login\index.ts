import request
    from '@/utils/request';
import Taro
    from '@tarojs/taro';

export const getHousingList = () =>
  request<any>({
    url: '/auth/housing/list',
    method: 'GET',
  });

export const getPhone = (data) =>
  request<any>({
    url: '/auth/miniapp/getPhone',
    method: 'POST',
    data,
  });

export const postRegister = (data) =>
  request<any>({
    url: '/auth/miniapp/register',
    method: 'POST',
    data,
  });

export const upload91OSS = (data) =>
  request<any>({
    url: '/resource/oss/upload',
    method: 'POST',
    data,
  });
export const login = (data) =>
  request<any>({
    url: '/auth/miniapp/login',
    method: 'POST',
    data,
  });
export const getRegisterStatus = () =>
  Taro.login().then((res) =>
    request<any>({
      url: '/auth/miniapp/resetStatus',
      method: 'GET',
      params: {
        jsCode: res.code,
      },
    }),
  );
export const logout = () =>
  request<any>({
    url: '/auth/logout',
    method: 'POST',
  });
export const getSmsCode = (phonenumber) =>
  request<any>({
    url: '/resource/sms/code',
    method: 'GET',
    params: { phonenumber },
  });
