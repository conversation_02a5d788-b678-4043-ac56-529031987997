// babel-preset-taro 更多选项和默认值：
// https://github.com/NervJS/taro/blob/next/packages/babel-preset-taro/README.md
module.exports = {
  presets: [
    ['taro', {
      framework: 'vue3',
      ts: true
    }]
  ],
  plugins: [
    [
      'import',
      {
        'libraryName': '@nutui/nutui-taro',
        'libraryDirectory': 'dist/packages/_es',
        'style': (name, file) => name.toLowerCase().replace('_es/', '') + '/index.scss',
        'camel2DashComponentName': false
      },
      'nutui3-taro'
    ],[
      'import',
      {
        'libraryName': '@fishui/taro-vue',
        'libraryDirectory': 'lib/components',
        'style': (name, file) => name + '/style.scss',
        'camel2DashComponentName': false
      },
      '@fishui/taro-vue'
    ],
    [
      'import',
      {
        'libraryName': 'lodash',
        'libraryDirectory': '',
      }
    ]
    
  ]
};
