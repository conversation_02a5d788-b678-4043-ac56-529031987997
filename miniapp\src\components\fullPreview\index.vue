<template>
  <view :class="{ [styles.fullPreview]: true, [styles.dispoint]: !props.back ,myfp:true }" @tap="handleClickPage">
    <view class="title">{{ props.title }}</view>
    <view class="logo">
      <image v-if="props.imgSrc" class="bgImg" :src="props.imgSrc" />
    </view>
    <image :src="bgImg" class="lptBg"></image>

    <!-- <view class="hypnotic-5"></view>
    <slot name="normalSlot"/> -->
  </view>

</template>

<script lang="ts" setup>
// @ts-ignore
import styles
    from './styles.scss';
import txImgSrc
    from '@/assets/images/lpt/logo.jpg';
import bgImg
    from '@/assets/images/lpt/bg.jpg';


interface Props {
  // 点击去菜单栏
  back: boolean;
  imgSrc: string;
  title: string;
  svgaUrl: string;
  svgaLoop: number;
}

const props = withDefaults(defineProps<Props>(), {
  back: true,
  title: '家园议事厅',
  svgaUrl: '',
  svgaLoop: 1,
  imgSrc: txImgSrc,
});
const emit = defineEmits(['back', 'finsh']);

const handleClickPage = () => {
  !!props.back && emit('back');

};
</script>
