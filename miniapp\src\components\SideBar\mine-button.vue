<template>
  <view class="icon fcc" style="margin-top: 8rpx" @tap="toMine">
    <view class="full" >
      退出登录
    </view>
  </view>
</template>
<script lang="ts" setup>
import {
    logout
} from '@/apis/login';
import {
    unauthorized
} from '@/utils/request';
import Taro
    from '@tarojs/taro';

const toMine = async()=>{
await logout();
  unauthorized();
  Taro.switchTab({ url: '/pages/login/index' });


};
</script>
