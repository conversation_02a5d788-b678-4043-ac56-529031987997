<template>
  <view :class="styles.myContainer">
    <navbar title="公告列表" />
    <!-- <nut-watermark :gap-x="20" font-color="rgba(0, 0, 0, .1)" :z-index="1" content="公告列表" /> -->
    <view class="announcementList">
      <nut-cell v-for="(item, index) in data.list" :key="index" class="announcementItem" @tap="showDetail(item)">
        <view class="leftContent" hover-class="none" hover-stop-propagation="false">
          <image mode="widthFix" src="@/assets/images/lpt/announcement.png" />
        </view>

        <view class="rightContent" hover-class="none" hover-stop-propagation="false">
          <view class="line">
            <text>公告方：</text>
            {{ item.housingName }}</view
          >
          <view class="line">
            <text>公告标题：</text>

            {{ item.noticeTitle }}</view
          >
          <view class="line">
            <text>发布时间：</text>
            {{ item.createTime }}</view
          >
          <view class="detail">查看公告内容</view>
        </view>
      </nut-cell>
      <nut-empty v-if="data.list.length === 0"></nut-empty>
    </view>

    <nut-popup
      v-model:visible="data.showPop"
      :style="{
        height: '550px',
        background: 'linear-gradient(180deg, #FBFBFD 0%, #F5F5F9 100%)',
      }"
      position="bottom"
      round
      class="mpmChoosePopup"
      :catch-move="false"
      close-on-click-overlay
    >
      <view class="mpmChoosePopupMain">
        <view class="mpmChoosePopupMainTitle">
          <view class="mpmChoosePopupMainHead">{{ data.title }}</view>
          <view
            class="mpmChoosePopupMainClose"
            @tap="
              () => {
                data.showPop = false;
              }
            "
          >
            <alIconfont name="icon-chacha" size="12" fill="#666666" />
          </view>
        </view>

        <view v-if="!!data.html" class="popContent">
          <rich-text :nodes="data.html" />
        </view>
        <nut-empty v-else />
        <view class="choosePopFooter">
          <nut-button
            type="primary"
            class="next"
            @tap="
              () => {
                data.showPop = false;
              }
            "
            >确认</nut-button
          >
        </view>
      </view>
    </nut-popup>
  </view>
  <!-- toast提示 -->
  <my-toast-components ref="myToast" :duration="2500" />
</template>
<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles
    from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import {
    getNotices
} from '@/apis/mine';

definePageConfig({ backgroundColor: '#f3f3fe' });

const myToast = ref<any>();

const data = reactive({
  html: '',
  title: '',
  showPop: false,
  list: [] as any,
});
const showDetail = (noticeContent) => {
  data.html = noticeContent.noticeContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto" ');
  data.title = noticeContent.noticeTitle;
  data.showPop = true;
};

const httpNotices = async () => {
  const res = await getNotices();
  data.list = res;
};

httpNotices();
</script>
